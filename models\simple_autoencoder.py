"""
Simple Autoencoder implementation for well log imputation.
This is a simplified version that doesn't depend on PyPOTS.
"""

import torch
import torch.nn as nn
import numpy as np
from .neuralnet import AENN, _reconstruction_loss

class SimpleAutoencoder:
    """
    A simplified autoencoder for well log imputation.
    """

    def __init__(self, n_features=4, sequence_len=64, encoding_dim=32,
                 epochs=10, batch_size=64, learning_rate=0.01):
        """
        Initialize the autoencoder.

        Args:
            n_features: Number of features in the input
            sequence_len: Length of input sequences
            encoding_dim: Dimension of the encoded representation
            epochs: Number of training epochs
            batch_size: Batch size for training
            learning_rate: Learning rate for optimization
        """
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.encoding_dim = encoding_dim
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate

        # Create a simple, stable autoencoder model
        input_size = sequence_len * n_features

        # Simple linear autoencoder for stability
        self.model = nn.Sequential(
            nn.Linear(input_size, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, encoding_dim),
            nn.<PERSON><PERSON>(),
            nn.Linear(encoding_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, input_size)
        )

        # Initialize weights properly
        self._initialize_weights()

        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-4)
        self.criterion = nn.MSELoss(reduction='none')  # Use reduction='none' for masking
        self.scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=5, gamma=0.8)

    def _initialize_weights(self):
        """Initialize model weights to prevent gradient explosion."""
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                # Use Xavier initialization with smaller gain for stability
                nn.init.xavier_uniform_(module.weight, gain=0.5)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
    def fit(self, train_data, truth_data, epochs=None, batch_size=None):
        """
        Train the autoencoder.

        Args:
            train_data: Training data with missing values (NaN values)
            truth_data: Complete data for training
            epochs: Number of epochs (optional)
            batch_size: Batch size (optional)
        """
        # Input validation
        if not isinstance(train_data, torch.Tensor) or not isinstance(truth_data, torch.Tensor):
            raise TypeError("train_data and truth_data must be torch.Tensor")

        if train_data.shape != truth_data.shape:
            raise ValueError(f"Shape mismatch: train_data {train_data.shape} vs truth_data {truth_data.shape}")

        if len(train_data.shape) != 3:
            raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {train_data.shape}")

        if train_data.shape[1] != self.sequence_len or train_data.shape[2] != self.n_features:
            raise ValueError(f"Data shape {train_data.shape} doesn't match model config (seq_len={self.sequence_len}, n_features={self.n_features})")

        if epochs is None:
            epochs = self.epochs
        if batch_size is None:
            batch_size = self.batch_size

        # Validate epochs and batch_size
        if epochs <= 0:
            raise ValueError("epochs must be positive")
        if batch_size <= 0:
            raise ValueError("batch_size must be positive")

        self.model.train()

        # Prepare data: replace NaN with zeros in training data, create masks
        train_clean = torch.nan_to_num(train_data, nan=0.0)  # Replace NaN with 0
        missing_mask = torch.isnan(train_data).float()  # 1 where data is missing

        # Use only samples with reasonable amount of data (>50% present)
        sample_missing_rate = missing_mask.view(missing_mask.size(0), -1).mean(dim=1)
        valid_samples = sample_missing_rate < 0.5

        if valid_samples.sum() < len(valid_samples) * 0.1:
            print("Warning: Too few valid samples after filtering, using all data")
            valid_samples = torch.ones(len(train_data), dtype=torch.bool)
        else:
            print(f"Filtered data: using {valid_samples.sum()}/{len(valid_samples)} samples (removed {(~valid_samples).sum()} high-missing samples)")
            train_clean = train_clean[valid_samples]
            truth_data = truth_data[valid_samples]
            missing_mask = missing_mask[valid_samples]

        # Flatten the input data
        train_flat = train_clean.view(train_clean.size(0), -1)
        truth_flat = truth_data.view(truth_data.size(0), -1)
        mask_flat = missing_mask.view(missing_mask.size(0), -1)

        print(f"Training simple autoencoder for {epochs} epochs...")
        print(f"Data shape: {train_flat.shape}")
        print(f"Missing data percentage: {missing_mask.mean().item():.2%}")

        # Simple training loop - use all data but replace NaN with 0
        # Replace NaN values with 0 for training
        train_no_nan = torch.nan_to_num(train_flat, nan=0.0)
        truth_no_nan = torch.nan_to_num(truth_flat, nan=0.0)

        print(f"Replaced NaN values with 0 for training")

        dataset = torch.utils.data.TensorDataset(train_no_nan, truth_no_nan, mask_flat)
        dataloader = torch.utils.data.DataLoader(dataset, batch_size=batch_size, shuffle=True)

        for epoch in range(epochs):
            epoch_loss = 0.0
            num_batches = 0

            for batch_input, batch_target, batch_mask in dataloader:
                self.optimizer.zero_grad()

                # Forward pass
                reconstructed = self.model(batch_input)

                # Compute masked loss.
                # The loss is only calculated on the original, non-missing data points.
                # This is achieved by creating a `valid_mask` that is 1 for non-missing values and 0 for missing values.
                # The loss per element is then multiplied by this mask, so that the loss for missing values is zero.
                loss_per_element = self.criterion(reconstructed, batch_target)
                valid_mask = 1.0 - batch_mask
                masked_loss = loss_per_element * valid_mask

                # Average loss only over valid (non-missing) elements
                valid_count = valid_mask.sum()
                if valid_count > 0:
                    loss = masked_loss.sum() / valid_count
                else:
                    loss = torch.tensor(0.0, requires_grad=True)


                # Check for numerical issues
                if torch.isnan(loss) or torch.isinf(loss):
                    continue

                # Backward pass
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                self.optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

            if num_batches > 0:
                avg_loss = epoch_loss / num_batches
                self.scheduler.step()

                if epoch % 2 == 0:
                    print(f"Epoch {epoch}/{epochs}, Loss: {avg_loss:.6f}")
            else:
                print(f"Epoch {epoch}/{epochs}: No valid batches")

        print("Training completed!")
    
    def predict(self, data):
        """
        Predict/reconstruct the full input.

        Args:
            data: Input data (can have missing values)

        Returns:
            Fully reconstructed data from the autoencoder.
        """
        # Input validation
        if not isinstance(data, torch.Tensor):
            raise TypeError("data must be torch.Tensor")

        if len(data.shape) != 3:
            raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {data.shape}")

        if data.shape[1] != self.sequence_len or data.shape[2] != self.n_features:
            raise ValueError(f"Data shape {data.shape} doesn't match model config (seq_len={self.sequence_len}, n_features={self.n_features})")

        self.model.eval()

        with torch.no_grad():
            # Replace NaN with zeros for model input, as done in training
            data_clean = torch.nan_to_num(data, nan=0.0)

            # Flatten the input data
            data_flat = data_clean.view(data_clean.size(0), -1)

            # Get reconstructed data
            reconstructed_flat = self.model(data_flat)

            # Reshape back to original 3D shape
            reconstructed_tensor = reconstructed_flat.view(data.shape)

        return reconstructed_tensor

    def evaluate_imputation(self, original_data, imputed_data, missing_mask):
        """
        Evaluate imputation performance only on originally missing values.

        Args:
            original_data: Original data with missing values
            imputed_data: Data with imputed values
            missing_mask: Boolean mask indicating originally missing values

        Returns:
            Dictionary with evaluation metrics (MAE, RMSE, R2)
        """
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
        import numpy as np

        # Convert to numpy if needed
        if isinstance(original_data, torch.Tensor):
            original_data = original_data.cpu().numpy()
        if isinstance(imputed_data, torch.Tensor):
            imputed_data = imputed_data.cpu().numpy()
        if isinstance(missing_mask, torch.Tensor):
            missing_mask = missing_mask.cpu().numpy()

        # Flatten arrays for evaluation
        original_flat = original_data.flatten()
        imputed_flat = imputed_data.flatten()
        missing_flat = missing_mask.flatten()

        # Get indices where we have both original and imputed values (for validation)
        valid_mask = ~np.isnan(original_flat) & ~np.isnan(imputed_flat) & ~missing_flat

        if valid_mask.sum() == 0:
            return {'mae': float('inf'), 'rmse': float('inf'), 'r2': -float('inf')}

        y_true = original_flat[valid_mask]
        y_pred = imputed_flat[valid_mask]

        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)

        return {'mae': mae, 'rmse': rmse, 'r2': r2}

class SimpleUNet:
    """
    A simplified U-Net placeholder for well log imputation.
    """

    def __init__(self, n_features=4, sequence_len=64, epochs=50, batch_size=32, learning_rate=0.0001):
        """
        Initialize the U-Net.
        """
        self.n_features = n_features
        self.sequence_len = sequence_len
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate

        # For now, use a simple linear model as placeholder
        self.model = nn.Sequential(
            nn.Linear(sequence_len * n_features, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Linear(128, sequence_len * n_features)
        )

        # Initialize weights properly
        self._initialize_weights()

        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        self.criterion = nn.MSELoss(reduction='none')  # Use reduction='none' for masking
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10
        )

    def _initialize_weights(self):
        """Initialize model weights to prevent gradient explosion."""
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
    def fit(self, train_data, truth_data, epochs=None, batch_size=None):
        """
        Train the U-Net.
        """
        if epochs is None:
            epochs = self.epochs
        if batch_size is None:
            batch_size = self.batch_size

        self.model.train()

        # Prepare data: replace NaN with zeros in training data, create masks
        train_clean = torch.nan_to_num(train_data, nan=0.0)  # Replace NaN with 0
        missing_mask = torch.isnan(train_data).float()  # 1 where data is missing

        # Flatten the input data
        train_flat = train_clean.view(train_clean.size(0), -1)
        truth_flat = truth_data.view(truth_data.size(0), -1)
        mask_flat = missing_mask.view(missing_mask.size(0), -1)

        print(f"Training U-Net for {epochs} epochs...")
        print(f"Data shape: {train_flat.shape}")
        print(f"Missing data percentage: {missing_mask.mean().item():.2%}")

        for epoch in range(epochs):
            total_loss = 0
            num_batches = (len(train_data) + batch_size - 1) // batch_size

            for i in range(num_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(train_data))

                batch_train = train_flat[start_idx:end_idx]
                batch_truth = truth_flat[start_idx:end_idx]
                batch_mask = mask_flat[start_idx:end_idx]

                # Forward pass
                self.optimizer.zero_grad()
                reconstructed = self.model(batch_train)

                # Compute masked loss (only on non-missing values)
                loss_per_element = self.criterion(reconstructed, batch_truth)
                # Create inverse mask (1 where data is present, 0 where missing)
                valid_mask = 1.0 - batch_mask
                masked_loss = loss_per_element * valid_mask

                # Average loss only over valid (non-missing) elements
                valid_count = valid_mask.sum()
                if valid_count > 0:
                    loss = masked_loss.sum() / valid_count
                else:
                    loss = torch.tensor(0.0, requires_grad=True)

                # Check for NaN loss
                if torch.isnan(loss):
                    print(f"Warning: NaN loss detected at epoch {epoch}, batch {i}")
                    continue

                # Backward pass with gradient clipping
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                total_loss += loss.item()

            if epoch % 10 == 0:
                avg_loss = total_loss / num_batches if num_batches > 0 else 0
                print(f"Epoch {epoch}/{epochs}, Loss: {avg_loss:.6f}")

        print("Training completed!")
    
    def predict(self, data):
        """
        Predict/reconstruct the full input.
        """
        # Input validation
        if not isinstance(data, torch.Tensor):
            raise TypeError("data must be torch.Tensor")

        if len(data.shape) != 3:
            raise ValueError(f"Expected 3D tensor (batch, sequence, features), got shape {data.shape}")

        if data.shape[1] != self.sequence_len or data.shape[2] != self.n_features:
            raise ValueError(f"Data shape {data.shape} doesn't match model config (seq_len={self.sequence_len}, n_features={self.n_features})")
            
        self.model.eval()

        with torch.no_grad():
            # Replace NaN with zeros for model input, as done in training
            data_clean = torch.nan_to_num(data, nan=0.0)

            # Flatten the input data
            data_flat = data_clean.view(data_clean.size(0), -1)

            # Get reconstructed data
            reconstructed_flat = self.model(data_flat)

            # Reshape back to original 3D shape
            reconstructed_tensor = reconstructed_flat.view(data.shape)

        return reconstructed_tensor
